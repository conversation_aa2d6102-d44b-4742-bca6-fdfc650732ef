import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import SEO from '@/components/SEO';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { getAllProducts, getAllCategories, getProductsByCategory, searchProducts } from '@/utils/productUtils';
import { generateCategorySEO, generateSearchSEO } from '@/utils/seoUtils';
import { ProductCard } from '@/components/product';
import { Search, Filter, Grid, List, Compare, X, Plus, Minus, SlidersHorizontal } from 'lucide-react';
import { useState, useMemo } from 'react';
import { Product } from '@/types/product';

const ProductsPage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [compareProducts, setCompareProducts] = useState<Product[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState<'name' | 'category' | 'newest'>('name');

  const allProducts = getAllProducts();
  const categories = getAllCategories();

  const filteredProducts = useMemo(() => {
    let products = allProducts;

    // Filter by category
    if (selectedCategory !== 'all') {
      products = getProductsByCategory(selectedCategory as any);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      products = searchProducts(searchQuery).filter(product =>
        selectedCategory === 'all' || product.category === selectedCategory
      );
    }

    // Sort products
    products.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.title.localeCompare(b.title);
        case 'category':
          return a.category.localeCompare(b.category);
        case 'newest':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        default:
          return 0;
      }
    });

    return products;
  }, [searchQuery, selectedCategory, sortBy, allProducts]);

  // Comparison functions
  const addToCompare = (product: Product) => {
    if (compareProducts.length < 3 && !compareProducts.find(p => p.id === product.id)) {
      setCompareProducts([...compareProducts, product]);
    }
  };

  const removeFromCompare = (productId: string) => {
    setCompareProducts(compareProducts.filter(p => p.id !== productId));
  };

  const clearComparison = () => {
    setCompareProducts([]);
  };

  // Generate SEO data based on current state
  const seoData = useMemo(() => {
    if (searchQuery.trim()) {
      return generateSearchSEO(searchQuery, filteredProducts.length);
    }

    if (selectedCategory !== 'all') {
      const categoryInfo = categories.find(c => c.id === selectedCategory);
      return generateCategorySEO(categoryInfo?.name || selectedCategory, filteredProducts.length);
    }

    return {
      title: 'Our Products - Nile Pro MEP Solutions',
      description: 'Comprehensive range of MEP products for all your construction and installation needs. Air handling units, condensing units, heat recovery systems, and more.',
      keywords: 'MEP Products, HVAC, Air Handling Unit, Condensing Unit, Heat Recovery, Ventilation, Engineering Solutions, Nile Pro',
      image: '/og-image.jpg',
      url: `${window.location.origin}/products`,
      type: 'website' as const
    };
  }, [searchQuery, selectedCategory, filteredProducts.length, categories]);

  return (
    <div className="min-h-screen overflow-x-hidden">
      <SEO {...seoData} />
      <Navigation />
      <div className="pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
              Our Products
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Comprehensive range of MEP products for all your construction and installation needs
            </p>
          </div>

          {/* Enhanced Search and Filter */}
          <div className="mb-12">
            <div className="flex flex-col lg:flex-row gap-4 mb-8">
              {/* Search Bar */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search products by name, description, or features..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 min-h-[44px]"
                />
              </div>

              {/* Controls */}
              <div className="flex gap-2">
                {/* Sort Dropdown */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="px-3 py-2 border border-border rounded-md bg-background text-foreground min-h-[44px] min-w-[120px]"
                >
                  <option value="name">Sort by Name</option>
                  <option value="category">Sort by Category</option>
                  <option value="newest">Sort by Newest</option>
                </select>

                {/* View Mode Toggle */}
                <div className="flex border border-border rounded-md overflow-hidden">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-none min-h-[44px]"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-none min-h-[44px]"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>

                {/* Advanced Filters Toggle */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                  className="min-h-[44px]"
                >
                  <SlidersHorizontal className="h-4 w-4 mr-2" />
                  Filters
                </Button>
              </div>
            </div>

            {/* Category Tabs */}
            <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
              <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-7 min-h-[44px]">
                <TabsTrigger value="all">All Products</TabsTrigger>
                {categories.map((category) => (
                  <TabsTrigger key={category.id} value={category.id}>
                    {category.name}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>

            {/* Advanced Filters Panel */}
            {showFilters && (
              <Card className="mt-4 border-border">
                <CardContent className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <h4 className="font-semibold text-foreground mb-3">Filter by Tags</h4>
                      <div className="flex flex-wrap gap-2">
                        {['Energy Efficient', 'Smart Controls', 'Eco-Friendly', 'High Performance', 'Compact Design'].map((tag) => (
                          <Badge
                            key={tag}
                            variant="outline"
                            className="cursor-pointer hover:bg-primary hover:text-white transition-colors"
                            onClick={() => setSearchQuery(tag)}
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground mb-3">Quick Filters</h4>
                      <div className="space-y-2">
                        <Button variant="outline" size="sm" onClick={() => setSearchQuery('HVAC')} className="w-full justify-start">
                          HVAC Systems
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => setSearchQuery('Energy')} className="w-full justify-start">
                          Energy Efficient
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => setSearchQuery('Smart')} className="w-full justify-start">
                          Smart Technology
                        </Button>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground mb-3">Actions</h4>
                      <div className="space-y-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSearchQuery('');
                            setSelectedCategory('all');
                            setSortBy('name');
                          }}
                          className="w-full justify-start"
                        >
                          Clear All Filters
                        </Button>
                        {compareProducts.length > 0 && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={clearComparison}
                            className="w-full justify-start"
                          >
                            Clear Comparison ({compareProducts.length})
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Results Summary and Comparison Bar */}
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <p className="text-muted-foreground">
                Showing {filteredProducts.length} product{filteredProducts.length !== 1 ? 's' : ''}
                {searchQuery && ` for "${searchQuery}"`}
                {selectedCategory !== 'all' && ` in ${categories.find(c => c.id === selectedCategory)?.name}`}
              </p>

              {/* Comparison Bar */}
              {compareProducts.length > 0 && (
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="bg-primary text-white">
                    <Compare className="h-3 w-3 mr-1" />
                    {compareProducts.length} selected
                  </Badge>
                  {compareProducts.length >= 2 && (
                    <Button size="sm" className="bg-gradient-primary hover:opacity-90">
                      Compare Products
                    </Button>
                  )}
                  <Button size="sm" variant="outline" onClick={clearComparison}>
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Products Display */}
          <div className={viewMode === 'grid'
            ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8"
            : "space-y-6"
          }>
            {filteredProducts.map((product) => (
              <div key={product.id} className="relative">
                <ProductCard
                  product={product}
                  showCategory={true}
                  showTags={true}
                  maxFeatures={viewMode === 'list' ? 5 : 3}
                  className={viewMode === 'list' ? "flex flex-col sm:flex-row" : ""}
                />

                {/* Compare Button */}
                <div className="absolute top-2 right-2">
                  {compareProducts.find(p => p.id === product.id) ? (
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => removeFromCompare(product.id)}
                      className="bg-primary text-white hover:bg-primary/90"
                    >
                      <Minus className="h-3 w-3" />
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => addToCompare(product)}
                      disabled={compareProducts.length >= 3}
                      className="bg-background/80 backdrop-blur-sm"
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* No Results */}
          {filteredProducts.length === 0 && (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <h3 className="text-lg font-semibold mb-2">No products found</h3>
                <p className="text-muted-foreground mb-4">
                  Try adjusting your search terms or category filter.
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('all');
                    setSortBy('name');
                  }}
                >
                  Clear All Filters
                </Button>
              </div>
            </div>
          )}

          {/* Product Comparison Modal */}
          {compareProducts.length >= 2 && (
            <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
              <Card className="w-full max-w-6xl max-h-[90vh] overflow-auto">
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-2xl">Product Comparison</CardTitle>
                  <Button variant="ghost" size="sm" onClick={clearComparison}>
                    <X className="h-4 w-4" />
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {compareProducts.map((product) => (
                      <div key={product.id} className="border border-border rounded-lg p-4">
                        <div className="aspect-video overflow-hidden rounded-lg mb-4">
                          <img
                            src={product.images[0]?.url}
                            alt={product.title}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <h3 className="font-bold text-lg mb-2">{product.title}</h3>
                        <Badge variant="secondary" className="mb-4">
                          {product.category.replace('-', ' ').toUpperCase()}
                        </Badge>

                        <div className="space-y-4">
                          <div>
                            <h4 className="font-semibold mb-2">Key Features</h4>
                            <ul className="space-y-1">
                              {product.features.slice(0, 4).map((feature, idx) => (
                                <li key={idx} className="text-sm text-muted-foreground flex items-start">
                                  <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                  {feature.title}
                                </li>
                              ))}
                            </ul>
                          </div>

                          <div>
                            <h4 className="font-semibold mb-2">Specifications</h4>
                            <div className="space-y-1">
                              {product.specifications.slice(0, 3).map((spec, idx) => (
                                <div key={idx} className="text-sm">
                                  <span className="text-muted-foreground">{spec.name}:</span>
                                  <span className="ml-2 font-medium">{spec.value} {spec.unit}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeFromCompare(product.id)}
                          className="w-full mt-4"
                        >
                          Remove from Comparison
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* CTA Section */}
          <div className="mt-20 text-center bg-gradient-to-r from-primary/10 to-accent/10 rounded-lg p-8">
            <h2 className="text-2xl font-bold mb-4">Need a Custom Solution?</h2>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Our engineering team can design and manufacture custom MEP solutions tailored to your specific requirements.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="w-full sm:w-auto bg-gradient-primary hover:opacity-90 shadow-primary min-h-[44px]">
                Contact Engineering Team
              </Button>
              <Button variant="outline" className="w-full sm:w-auto min-h-[44px]">
                Download Product Catalog
              </Button>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ProductsPage;